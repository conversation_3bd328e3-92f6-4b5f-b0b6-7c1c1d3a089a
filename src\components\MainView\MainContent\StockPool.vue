<script setup lang="tsx">
import { onMounted, shallowRef, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import type {
  ColumnDefinition,
  RowAction,
  PoolStock,
  QueryInstrumentResponse,
  FilterBody,
} from '@/types';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import { PoolService } from '@/api';
import { Misc } from '@/script';

// 股票池数据
const poolStocks = shallowRef<PoolStock[]>([]);

// 选中的股票值
const selectedStock = shallowRef('');

// 远程搜索结果
const searchResults = shallowRef<QueryInstrumentResponse[]>([]);

// 搜索加载状态
const searchLoading = shallowRef(false);

// 表格列定义
const columns: ColumnDefinition<PoolStock> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '股票代码',
    width: 200,
  },
  {
    key: 'instrument_name',
    dataKey: 'instrument_name',
    title: '股票名称',
    width: 200,
  },
  {
    key: 'pct',
    dataKey: 'pct',
    title: '当前涨幅',
    width: 200,
    cellRenderer: ({ cellData }) => <span>{cellData}%</span>,
  },
  {
    key: 'avg_pct',
    dataKey: 'avg_pct',
    title: '均价涨幅',
    width: 200,
    cellRenderer: ({ cellData }) => <span>{cellData}%</span>,
  },
  {
    key: 'join_time',
    dataKey: 'join_time',
    title: '加入时间',
    width: 200,
    cellRenderer: ({ cellData }) => <span>{cellData.slice(0, 19)}</span>,
  },
  {
    key: 'is_ok',
    dataKey: 'is_ok',
    title: '实时情况',
    width: 200,
    cellRenderer: ({ cellData }) => <span>{cellData ? '满足条件' : '不满足条件'}</span>,
  },
  // {
  //   key: 'trading_day',
  //   dataKey: 'trading_day',
  //   title: '交易日',
  //   width: 120,
  // },
  {
    key: 'join_type',
    dataKey: 'join_type',
    title: '入池类型',
    width: 100,
    cellRenderer: ({ cellData }) => <span>{cellData === 1 ? '自动' : '手动'}</span>,
  },
];

// 行操作定义
const rowActions: RowAction<PoolStock>[] = [
  {
    label: '删除',
    color: 'var(--g-red)',
    onClick: handleDelete,
    show: rowData => {
      // 只有手动入池的股票且用户有手动入池权限时才显示删除按钮
      return rowData.join_type === 2 && Misc.hasPoolPermission();
    },
  },
];

// 获取股票池数据
const getPoolStocks = async () => {
  const { error_code, error_msg, data } = await PoolService.getPoolStocks();
  if (error_code === 0 && data) {
    poolStocks.value = data;
  } else {
    ElMessage.error(error_msg || '获取股票池数据失败');
  }
};

// 远程搜索股票
const remoteSearch = async (query: string) => {
  if (!query.trim()) {
    searchResults.value = [];
    return;
  }

  searchLoading.value = true;
  try {
    const { error_code, error_msg, data } = await PoolService.queryInstrument(query);
    if (error_code === 0 && data) {
      searchResults.value = data;
    } else {
      searchResults.value = [];
      ElMessage.error(error_msg || '搜索股票失败');
    }
  } catch (error) {
    console.error('搜索股票失败:', error);
    searchResults.value = [];
    ElMessage.error('搜索股票失败');
  } finally {
    searchLoading.value = false;
  }
};

// 选择股票并手动入池
const handleStockSelect = async (value: string) => {
  const stock = searchResults.value.find(item => item.stock_code === value);
  if (!stock) return;

  const { error_code, error_msg, data } = await PoolService.addPoolStock({
    instrument: stock.stock_code,
    instrument_name: stock.stock_name,
  });

  if (error_code === 0 && data) {
    ElMessage.success('股票入池成功');
    // 更新本地数据
    Misc.putRow(data, poolStocks);
    // 清空搜索
    selectedStock.value = '';
    searchResults.value = [];
  } else {
    ElMessage.error(error_msg || '股票入池失败');
  }
};

// 删除股票
async function handleDelete(rowData: PoolStock) {
  try {
    await ElMessageBox.confirm(
      `确定要删除股票 ${rowData.instrument_name}(${rowData.instrument}) 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    );

    const { error_code, error_msg } = await PoolService.deletePoolStock(rowData.id);
    if (error_code === 0) {
      ElMessage.success('删除成功');
      // 从本地数据中移除
      poolStocks.value = poolStocks.value.filter(item => item.id !== rowData.id);
    } else {
      ElMessage.error(error_msg || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除股票失败:', error);
      ElMessage.error('删除失败');
    }
  }
}

// 格式化搜索结果用于虚拟化选择器
const formatSearchOptions = () => {
  return searchResults.value.map(item => ({
    label: `${item.stock_code} ${item.stock_name}`,
    value: item.stock_code,
    instrument_name: item.stock_name,
  }));
};

// 组件挂载时获取数据
onMounted(() => {
  getPoolStocks();
  setInterval(() => {
    getPoolStocks();
  }, 3000);
});
</script>

<template>
  <div>
    <div flex aic jcc>
      <!-- TODO: 筛选条件 -->
    </div>
    <VirtualizedTable
      select
      show-index
      :columns="columns"
      :data="poolStocks"
      :row-actions="rowActions"
      :row-action-width="80"
    >
      <template #toolbar>
        <!-- 只有具有手动入池权限的用户才能看到搜索框 -->
        <div v-if="Misc.hasPoolPermission()" flex aic gap-10>
          <div>手动入池：</div>
          <el-select-v2
            v-model="selectedStock"
            :options="formatSearchOptions()"
            :loading="searchLoading"
            placeholder="搜索股票代码或名称"
            clearable
            filterable
            remote
            :remote-method="remoteSearch"
            class="w-200!"
            @change="handleStockSelect"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-select-v2>
        </div>
        <div v-else></div>
        <div class="actions" flex aic jce>
          <el-button @click="getPoolStocks" size="small" color="var(--g-primary)">刷新</el-button>
        </div>
      </template>
    </VirtualizedTable>
  </div>
</template>

<style scoped></style>
