<script setup lang="tsx">
import { onMounted, shallowRef, ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import type {
  ColumnDefinition,
  RowAction,
  PoolStock,
  QueryInstrumentResponse,
  FilterBody,
  FilterCondition,
} from '@/types';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import { PoolService, ScreeningService } from '@/api';
import type { PlateOption } from '@/api/screening';
import { Misc } from '@/script';

// 股票池数据
const poolStocks = shallowRef<PoolStock[]>([]);

// 选中的股票值
const selectedStock = shallowRef('');

// 远程搜索结果
const searchResults = shallowRef<QueryInstrumentResponse[]>([]);

// 搜索加载状态
const searchLoading = shallowRef(false);

// 筛选条件相关状态
const filterCondition = ref<FilterCondition>({});
const plateOptions = shallowRef<PlateOption[]>([]);
const currentFilterBody = shallowRef<FilterBody | null>(null);
const filterLoading = shallowRef(false);

// 表格列定义
const columns: ColumnDefinition<PoolStock> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '股票代码',
    width: 200,
  },
  {
    key: 'instrument_name',
    dataKey: 'instrument_name',
    title: '股票名称',
    width: 200,
  },
  {
    key: 'pct',
    dataKey: 'pct',
    title: '当前涨幅',
    width: 200,
    cellRenderer: ({ cellData }) => <span>{cellData}%</span>,
  },
  {
    key: 'avg_pct',
    dataKey: 'avg_pct',
    title: '均价涨幅',
    width: 200,
    cellRenderer: ({ cellData }) => <span>{cellData}%</span>,
  },
  {
    key: 'join_time',
    dataKey: 'join_time',
    title: '加入时间',
    width: 200,
    cellRenderer: ({ cellData }) => <span>{cellData.slice(0, 19)}</span>,
  },
  {
    key: 'is_ok',
    dataKey: 'is_ok',
    title: '实时情况',
    width: 200,
    cellRenderer: ({ cellData }) => <span>{cellData ? '满足条件' : '不满足条件'}</span>,
  },
  // {
  //   key: 'trading_day',
  //   dataKey: 'trading_day',
  //   title: '交易日',
  //   width: 120,
  // },
  {
    key: 'join_type',
    dataKey: 'join_type',
    title: '入池类型',
    width: 100,
    cellRenderer: ({ cellData }) => <span>{cellData === 1 ? '自动' : '手动'}</span>,
  },
];

// 行操作定义
const rowActions: RowAction<PoolStock>[] = [
  {
    label: '删除',
    color: 'var(--g-red)',
    onClick: handleDelete,
    show: rowData => {
      // 只有手动入池的股票且用户有手动入池权限时才显示删除按钮
      return rowData.join_type === 2 && Misc.hasPoolPermission();
    },
  },
];

// 获取股票池数据
const getPoolStocks = async () => {
  const { error_code, error_msg, data } = await PoolService.getPoolStocks();
  if (error_code === 0 && data) {
    poolStocks.value = data;
  } else {
    ElMessage.error(error_msg || '获取股票池数据失败');
  }
};

// 远程搜索股票
const remoteSearch = async (query: string) => {
  if (!query.trim()) {
    searchResults.value = [];
    return;
  }

  searchLoading.value = true;
  try {
    const { error_code, error_msg, data } = await PoolService.queryInstrument(query);
    if (error_code === 0 && data) {
      searchResults.value = data;
    } else {
      searchResults.value = [];
      ElMessage.error(error_msg || '搜索股票失败');
    }
  } catch (error) {
    console.error('搜索股票失败:', error);
    searchResults.value = [];
    ElMessage.error('搜索股票失败');
  } finally {
    searchLoading.value = false;
  }
};

// 选择股票并手动入池
const handleStockSelect = async (value: string) => {
  const stock = searchResults.value.find(item => item.stock_code === value);
  if (!stock) return;

  const { error_code, error_msg, data } = await PoolService.addPoolStock({
    instrument: stock.stock_code,
    instrument_name: stock.stock_name,
  });

  if (error_code === 0 && data) {
    ElMessage.success('股票入池成功');
    // 更新本地数据
    Misc.putRow(data, poolStocks);
    // 清空搜索
    selectedStock.value = '';
    searchResults.value = [];
  } else {
    ElMessage.error(error_msg || '股票入池失败');
  }
};

// 删除股票
async function handleDelete(rowData: PoolStock) {
  try {
    await ElMessageBox.confirm(
      `确定要删除股票 ${rowData.instrument_name}(${rowData.instrument}) 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    );

    const { error_code, error_msg } = await PoolService.deletePoolStock(rowData.id);
    if (error_code === 0) {
      ElMessage.success('删除成功');
      // 从本地数据中移除
      poolStocks.value = poolStocks.value.filter(item => item.id !== rowData.id);
    } else {
      ElMessage.error(error_msg || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除股票失败:', error);
      ElMessage.error('删除失败');
    }
  }
}

// 格式化搜索结果用于虚拟化选择器
const formatSearchOptions = () => {
  return searchResults.value.map(item => ({
    label: `${item.stock_code} ${item.stock_name}`,
    value: item.stock_code,
    instrument_name: item.stock_name,
  }));
};

// 计算属性：判断是否有筛选条件
const hasFilterConditions = computed(() => {
  const condition = filterCondition.value;
  return !!(
    condition.plate ||
    condition.pct !== undefined ||
    condition.volume !== undefined ||
    condition.avg_pct !== undefined ||
    condition.pre_pct !== undefined ||
    condition.pre_5_pct !== undefined
  );
});

// 计算属性：开始按钮是否禁用
const isStartDisabled = computed(() => {
  return !hasFilterConditions.value || currentFilterBody.value?.status === 1;
});

// 计算属性：停止按钮是否禁用
const isStopDisabled = computed(() => {
  return !currentFilterBody.value || currentFilterBody.value.status === 2;
});

// 获取板块选项
const getPlateOptions = async () => {
  try {
    const { error_code, error_msg, data } = await ScreeningService.getPlates();
    if (error_code === 0 && data) {
      plateOptions.value = data;
    } else {
      ElMessage.error(error_msg || '获取板块选项失败');
    }
  } catch (error) {
    console.error('获取板块选项失败:', error);
    ElMessage.error('获取板块选项失败');
  }
};

// 获取当前筛选状态
const getFilterBody = async () => {
  try {
    const { error_code, error_msg, data } = await ScreeningService.getFilterBody();
    if (error_code === 0 && data) {
      currentFilterBody.value = data;
      // 解析筛选条件
      if (data.filter_condition) {
        try {
          filterCondition.value = JSON.parse(data.filter_condition);
        } catch (e) {
          console.error('解析筛选条件失败:', e);
        }
      }
    } else {
      currentFilterBody.value = null;
    }
  } catch (error) {
    console.error('获取筛选状态失败:', error);
    currentFilterBody.value = null;
  }
};

// 开始筛选
const startScreening = async () => {
  if (!hasFilterConditions.value) {
    ElMessage.warning('请至少填写一个筛选条件');
    return;
  }

  filterLoading.value = true;
  try {
    // 构建筛选条件对象，只包含有值的字段
    const condition: FilterCondition = {};
    if (filterCondition.value.plate) condition.plate = filterCondition.value.plate;
    if (filterCondition.value.pct !== undefined) condition.pct = filterCondition.value.pct;
    if (filterCondition.value.volume !== undefined) condition.volume = filterCondition.value.volume;
    if (filterCondition.value.avg_pct !== undefined)
      condition.avg_pct = filterCondition.value.avg_pct;
    if (filterCondition.value.pre_pct !== undefined)
      condition.pre_pct = filterCondition.value.pre_pct;
    if (filterCondition.value.pre_5_pct !== undefined)
      condition.pre_5_pct = filterCondition.value.pre_5_pct;

    const { error_code, error_msg, data } = await ScreeningService.start({
      user_id: Misc.getUser()?.id || 0,
      user_name: Misc.getUser()?.user_name || '',
      filter_condition: JSON.stringify(condition),
      status: 1,
    });

    if (error_code === 0 && data) {
      ElMessage.success('筛选已开始');
      currentFilterBody.value = data;
    } else {
      ElMessage.error(error_msg || '开始筛选失败');
    }
  } catch (error) {
    console.error('开始筛选失败:', error);
    ElMessage.error('开始筛选失败');
  } finally {
    filterLoading.value = false;
  }
};

// 停止筛选
const stopScreening = async () => {
  if (!currentFilterBody.value) return;

  filterLoading.value = true;
  try {
    const { error_code, error_msg, data } = await ScreeningService.stop(currentFilterBody.value);
    if (error_code === 0 && data) {
      ElMessage.success('筛选已停止');
      currentFilterBody.value = data;
    } else {
      ElMessage.error(error_msg || '停止筛选失败');
    }
  } catch (error) {
    console.error('停止筛选失败:', error);
    ElMessage.error('停止筛选失败');
  } finally {
    filterLoading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  getPoolStocks();
  // 只有管理员才能看到筛选功能
  if (Misc.isAdmin()) {
    getPlateOptions();
    getFilterBody();
  }
  setInterval(() => {
    getPoolStocks();
    if (Misc.isAdmin()) {
      getFilterBody();
    }
  }, 3000);
});
</script>

<template>
  <div>
    <!-- 筛选条件板块 - 只有管理员可见 -->
    <div v-if="Misc.isAdmin()" class="filter-panel" p-16 mb-16 bg="[--g-panel-bg]" rounded-8>
      <div class="filter-title" mb-12 fs-16 fw-600 c-white>筛选条件</div>
      <div class="filter-form" grid="~ cols-4 gap-16">
        <!-- 排除板块 -->
        <div>
          <div class="label" mb-8 fs-14 c="[--g-text-secondary]">排除板块</div>
          <el-select
            v-model="filterCondition.plate"
            :options="plateOptions"
            placeholder="选择板块"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            class="w-full!"
          />
        </div>

        <!-- 当日涨幅 -->
        <div>
          <div class="label" mb-8 fs-14 c="[--g-text-secondary]">当日涨幅 > (%)</div>
          <el-input-number
            v-model="filterCondition.pct"
            :min="-20"
            :max="20"
            :precision="2"
            placeholder="请输入"
            class="w-full!"
          />
        </div>

        <!-- 当日成交量 -->
        <div>
          <div class="label" mb-8 fs-14 c="[--g-text-secondary]">当日成交量 ></div>
          <el-input-number
            v-model="filterCondition.volume"
            :min="0"
            placeholder="请输入"
            class="w-full!"
          />
        </div>

        <!-- 当日均价涨跌幅 -->
        <div>
          <div class="label" mb-8 fs-14 c="[--g-text-secondary]">当日均价涨跌幅 > (%)</div>
          <el-input-number
            v-model="filterCondition.avg_pct"
            :min="-20"
            :max="20"
            :precision="2"
            placeholder="请输入"
            class="w-full!"
          />
        </div>

        <!-- 昨日涨幅 -->
        <div>
          <div class="label" mb-8 fs-14 c="[--g-text-secondary]">昨日涨幅 &lt; (%)</div>
          <el-input-number
            v-model="filterCondition.pre_pct"
            :min="-20"
            :max="20"
            :precision="2"
            placeholder="请输入"
            class="w-full!"
          />
        </div>

        <!-- 近一周涨幅 -->
        <div>
          <div class="label" mb-8 fs-14 c="[--g-text-secondary]">近一周涨幅 &lt; (%)</div>
          <el-input-number
            v-model="filterCondition.pre_5_pct"
            :min="-20"
            :max="20"
            :precision="2"
            placeholder="请输入"
            class="w-full!"
          />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="filter-actions" mt-16 flex aic gap-12>
        <el-button
          type="primary"
          :disabled="isStartDisabled"
          :loading="filterLoading"
          @click="startScreening"
        >
          开始
        </el-button>
        <el-button
          type="danger"
          :disabled="isStopDisabled"
          :loading="filterLoading"
          @click="stopScreening"
        >
          停止
        </el-button>
        <div v-if="currentFilterBody" class="status-info" ml-16 fs-14>
          <span c="[--g-text-secondary]">当前状态：</span>
          <span :class="currentFilterBody.status === 1 ? 'c-green' : 'c-red'">
            {{ currentFilterBody.status === 1 ? '运行中' : '已停止' }}
          </span>
        </div>
      </div>
    </div>
    <VirtualizedTable
      select
      show-index
      :columns="columns"
      :data="poolStocks"
      :row-actions="rowActions"
      :row-action-width="80"
    >
      <template #toolbar>
        <!-- 只有具有手动入池权限的用户才能看到搜索框 -->
        <div v-if="Misc.hasPoolPermission()" flex aic gap-10>
          <div>手动入池：</div>
          <el-select-v2
            v-model="selectedStock"
            :options="formatSearchOptions()"
            :loading="searchLoading"
            placeholder="搜索股票代码或名称"
            clearable
            filterable
            remote
            :remote-method="remoteSearch"
            class="w-200!"
            @change="handleStockSelect"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-select-v2>
        </div>
        <div v-else></div>
        <div class="actions" flex aic jce>
          <el-button @click="getPoolStocks" size="small" color="var(--g-primary)">刷新</el-button>
        </div>
      </template>
    </VirtualizedTable>
  </div>
</template>

<style scoped></style>
