<script setup lang="tsx">
import { onMounted, shallowRef } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import type { ColumnDefinition, RowAction, PoolStock, QueryInstrumentResponse } from '@/types';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import { PoolService } from '@/api';
import { Misc } from '@/script';

// 股票池数据
const poolStocks = shallowRef<PoolStock[]>([]);

// 搜索关键词
const searchKeyword = shallowRef('');

// 远程搜索结果
const searchResults = shallowRef<Array<Pick<PoolStock, 'instrument' | 'instrument_name'>>>([]);

// 搜索加载状态
const searchLoading = shallowRef(false);

// 表格列定义
const columns: ColumnDefinition<PoolStock> = [
  {
    key: 'instrument_name',
    dataKey: 'instrument_name',
    title: '股票名称',
    width: 150,
  },
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '股票代码',
    width: 120,
  },
  {
    key: 'trading_day',
    dataKey: 'trading_day',
    title: '交易日',
    width: 120,
  },
  {
    key: 'join_type',
    dataKey: 'join_type',
    title: '入池类型',
    width: 100,
    cellRenderer: ({ cellData }) => <span>{cellData === 1 ? '自动' : '手动'}</span>,
  },
];

// 行操作定义
const rowActions: RowAction<PoolStock>[] = [
  {
    label: '删除',
    color: '#f56c6c',
    onClick: handleDelete,
    show: rowData => {
      // 只有手动入池的股票且用户有手动入池权限时才显示删除按钮
      return rowData.join_type === 2 && Misc.hasPoolPermission();
    },
  },
];

// 获取股票池数据
const getPoolStocks = async () => {
  const { error_code, error_msg, data } = await PoolService.getPoolStocks();
  if (error_code === 0 && data) {
    poolStocks.value = data;
  } else {
    ElMessage.error(error_msg || '获取股票池数据失败');
  }
};

// 远程搜索股票
const remoteSearch = async (query: string) => {
  if (!query.trim()) {
    searchResults.value = [];
    return;
  }

  searchLoading.value = true;
  try {
    const { error_code, error_msg, data } = await PoolService.queryInstrument(query);
    if (error_code === 0 && data) {
      searchResults.value = data;
    } else {
      searchResults.value = [];
      ElMessage.error(error_msg || '搜索股票失败');
    }
  } catch (error) {
    console.error('搜索股票失败:', error);
    searchResults.value = [];
    ElMessage.error('搜索股票失败');
  } finally {
    searchLoading.value = false;
  }
};

// 选择股票并手动入池
const handleStockSelect = async (item: any) => {
  const stock = item.item as PoolStock;
  const { error_code, error_msg, data } = await PoolService.addPoolStock({
    instrument: stock.instrument,
    instrument_name: stock.instrument_name,
  });

  if (error_code === 0 && data) {
    ElMessage.success('股票入池成功');
    // 更新本地数据
    Misc.putRow(data, poolStocks);
    // 清空搜索
    searchKeyword.value = '';
    searchResults.value = [];
  } else {
    ElMessage.error(error_msg || '股票入池失败');
  }
};

// 删除股票
async function handleDelete(rowData: PoolStock) {
  try {
    await ElMessageBox.confirm(
      `确定要删除股票 ${rowData.instrument_name}(${rowData.instrument}) 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    );

    const { error_code, error_msg } = await PoolService.deletePoolStock(rowData.id);
    if (error_code === 0) {
      ElMessage.success('删除成功');
      // 从本地数据中移除
      poolStocks.value = poolStocks.value.filter(item => item.id !== rowData.id);
    } else {
      ElMessage.error(error_msg || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除股票失败:', error);
      ElMessage.error('删除失败');
    }
  }
}

// 格式化搜索结果用于显示
const formatSearchResults = (results: QueryInstrumentResponse[]) => {
  return results.map(item => ({
    value: `${item.instrument} ${item.instrument_name}`,
    item,
  }));
};

// 组件挂载时获取数据
onMounted(() => {
  getPoolStocks();
});
</script>

<template>
  <div>
    <VirtualizedTable
      :columns="columns"
      :data="poolStocks"
      :row-actions="rowActions"
      :row-action-width="80"
    >
      <template #toolbar>
        <div flex aic>
          <!-- 只有具有手动入池权限的用户才能看到搜索框 -->
          <el-autocomplete
            v-if="Misc.hasPoolPermission()"
            v-model="searchKeyword"
            :fetch-suggestions="
              (query, cb) => {
                remoteSearch(query);
                cb(formatSearchResults(searchResults));
              }
            "
            :loading="searchLoading"
            placeholder="搜索股票代码或名称"
            clearable
            class="w-200!"
            @select="handleStockSelect"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #default="{ item }">
              <div flex justify-between>
                <span mr-15>{{ item.item.instrument }}</span>
                <span>{{ item.item.instrument_name }}</span>
              </div>
            </template>
          </el-autocomplete>
        </div>
        <div></div>
      </template>
    </VirtualizedTable>
  </div>
</template>

<style scoped></style>
