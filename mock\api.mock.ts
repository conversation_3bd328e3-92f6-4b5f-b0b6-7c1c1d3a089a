import { defineMock } from 'vite-plugin-mock-dev-server';
import { PoolStock } from '../src/types';
import {
  PositionDirectionEnum,
  TradeDirectionEnum,
  OrderStatusEnum,
  PositionEffectEnum,
} from '../src/enum';

// 定义表格相关类型（这些类型在mock中直接定义）

// 模拟股票池数据
const mockPoolStocks: PoolStock[] = [
  {
    id: 1,
    instrument: '600036',
    instrument_name: '招商银行',
    trading_day: '2023-12-19',
    filter_msg: '自动筛选',
    join_type: 1,
  },
  {
    id: 2,
    instrument: '600519',
    instrument_name: '贵州茅台',
    trading_day: '2023-12-19',
    filter_msg: '手动添加',
    join_type: 2,
  },
  {
    id: 3,
    instrument: '601398',
    instrument_name: '工商银行',
    trading_day: '2023-12-19',
    filter_msg: '自动筛选',
    join_type: 1,
  },
  {
    id: 4,
    instrument: '601288',
    instrument_name: '农业银行',
    trading_day: '2023-12-19',
    filter_msg: '手动添加',
    join_type: 2,
  },
  {
    id: 5,
    instrument: '601988',
    instrument_name: '中国银行',
    trading_day: '2023-12-19',
    filter_msg: '自动筛选',
    join_type: 1,
  },
  {
    id: 6,
    instrument: '601628',
    instrument_name: '中国人寿',
    trading_day: '2023-12-19',
    filter_msg: '手动添加',
    join_type: 2,
  },
  {
    id: 7,
    instrument: '601318',
    instrument_name: '中国平安',
    trading_day: '2023-12-19',
    filter_msg: '自动筛选',
    join_type: 1,
  },
];

// 模拟搜索股票数据
const mockSearchStocks: PoolStock[] = [
  {
    id: 101,
    instrument: '000001',
    instrument_name: '平安银行',
    trading_day: '2023-12-19',
    filter_msg: '',
    join_type: 2,
  },
  {
    id: 102,
    instrument: '000002',
    instrument_name: '万科A',
    trading_day: '2023-12-19',
    filter_msg: '',
    join_type: 2,
  },
  {
    id: 103,
    instrument: '000858',
    instrument_name: '五粮液',
    trading_day: '2023-12-19',
    filter_msg: '',
    join_type: 2,
  },
  {
    id: 104,
    instrument: '002415',
    instrument_name: '海康威视',
    trading_day: '2023-12-19',
    filter_msg: '',
    join_type: 2,
  },
  {
    id: 105,
    instrument: '002594',
    instrument_name: '比亚迪',
    trading_day: '2023-12-19',
    filter_msg: '',
    join_type: 2,
  },
];

let poolStockIdCounter = 8;

export default defineMock([
  // 股票池相关API
  // {
  //   url: '/api/pool/info',
  //   method: 'GET',
  //   body: () => {
  //     return {
  //       error_code: 0,
  //       data: mockPoolStocks,
  //     };
  //   },
  // },
  // {
  //   url: '/api/pool/info',
  //   method: 'POST',
  //   body: req => {
  //     const { instrument, instrument_name } = req.body;
  //     const newStock: PoolStock = {
  //       id: poolStockIdCounter++,
  //       instrument,
  //       instrument_name,
  //       trading_day: new Date().toISOString().split('T')[0],
  //       filter_msg: '手动添加',
  //       join_type: 2,
  //     };
  //     mockPoolStocks.push(newStock);
  //     return {
  //       error_code: 0,
  //       data: newStock,
  //     };
  //   },
  // },
  // {
  //   url: '/api/pool/info',
  //   method: 'DELETE',
  //   body: req => {
  //     const { id } = req.query;
  //     const index = mockPoolStocks.findIndex(stock => stock.id === Number(id));
  //     if (index > -1) {
  //       mockPoolStocks.splice(index, 1);
  //       return {
  //         error_code: 0,
  //       };
  //     }
  //     return {
  //       error_code: 1,
  //       error_msg: '股票不存在',
  //     };
  //   },
  // },
  // {
  //   url: '/api/pool/instrument',
  //   method: 'GET',
  //   body: req => {
  //     const { name } = req.query;
  //     if (!name) {
  //       return {
  //         error_code: 0,
  //         data: [],
  //       };
  //     }
  //     // 模拟搜索逻辑：根据股票代码或名称搜索
  //     const searchTerm = name.toLowerCase();
  //     const results = mockSearchStocks.filter(
  //       stock =>
  //         stock.instrument.toLowerCase().includes(searchTerm) ||
  //         stock.instrument_name.toLowerCase().includes(searchTerm),
  //     );
  //     return {
  //       error_code: 0,
  //       data: results,
  //     };
  //   },
  // },
]);
