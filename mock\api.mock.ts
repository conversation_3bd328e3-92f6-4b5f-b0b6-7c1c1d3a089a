import { defineMock } from 'vite-plugin-mock-dev-server';
import { PoolStock } from '../src/types';

// 定义表格相关类型（这些类型在mock中直接定义）

// 模拟股票池数据
const mockPoolStocks: PoolStock[] = [
  {
    id: 1,
    instrument: '600036',
    instrument_name: '招商银行',
    trading_day: '2023-12-19',
    filter_msg: '自动筛选',
    join_type: 1,
    pct: 2.5,
    avg_pct: 1.8,
    join_time: '2023-12-19 09:30:00',
    is_ok: true,
  },
  {
    id: 2,
    instrument: '600519',
    instrument_name: '贵州茅台',
    trading_day: '2023-12-19',
    filter_msg: '手动添加',
    join_type: 2,
    pct: -1.2,
    avg_pct: -0.8,
    join_time: '2023-12-19 10:15:00',
    is_ok: false,
  },
  {
    id: 3,
    instrument: '601398',
    instrument_name: '工商银行',
    trading_day: '2023-12-19',
    filter_msg: '自动筛选',
    join_type: 1,
    pct: 1.8,
    avg_pct: 1.2,
    join_time: '2023-12-19 09:30:00',
    is_ok: true,
  },
  {
    id: 4,
    instrument: '601288',
    instrument_name: '农业银行',
    trading_day: '2023-12-19',
    filter_msg: '手动添加',
    join_type: 2,
    pct: 0.5,
    avg_pct: 0.3,
    join_time: '2023-12-19 11:20:00',
    is_ok: true,
  },
  {
    id: 5,
    instrument: '601988',
    instrument_name: '中国银行',
    trading_day: '2023-12-19',
    filter_msg: '自动筛选',
    join_type: 1,
    pct: 3.2,
    avg_pct: 2.8,
    join_time: '2023-12-19 09:30:00',
    is_ok: true,
  },
  {
    id: 6,
    instrument: '601628',
    instrument_name: '中国人寿',
    trading_day: '2023-12-19',
    filter_msg: '手动添加',
    join_type: 2,
    pct: -0.8,
    avg_pct: -0.5,
    join_time: '2023-12-19 14:30:00',
    is_ok: false,
  },
  {
    id: 7,
    instrument: '601318',
    instrument_name: '中国平安',
    trading_day: '2023-12-19',
    filter_msg: '自动筛选',
    join_type: 1,
    pct: 4.1,
    avg_pct: 3.5,
    join_time: '2023-12-19 13:45:00',
    is_ok: true,
  },
];

// 模拟搜索股票数据
const mockSearchStocks = [
  {
    stock_code: '000001',
    stock_name: '平安银行',
  },
  {
    stock_code: '000002',
    stock_name: '万科A',
  },
  {
    stock_code: '000858',
    stock_name: '五粮液',
  },
  {
    stock_code: '002415',
    stock_name: '海康威视',
  },
  {
    stock_code: '002594',
    stock_name: '比亚迪',
  },
];

const poolStockIdCounter = 8;

// 模拟板块选项
const mockPlateOptions = [
  { label: '科技股', value: '科技股' },
  { label: '金融股', value: '金融股' },
  { label: '医药股', value: '医药股' },
  { label: '消费股', value: '消费股' },
  { label: '地产股', value: '地产股' },
  { label: '能源股', value: '能源股' },
];

// 模拟筛选状态
let mockFilterBody: any = null;

export default defineMock([
  // 板块选项API
  {
    url: '/api/screening/plates',
    method: 'GET',
    body: () => {
      return {
        error_code: 0,
        data: mockPlateOptions,
      };
    },
  },

  // 获取筛选状态API
  {
    url: '/api/screening/condition',
    method: 'GET',
    body: () => {
      return {
        error_code: 0,
        data: mockFilterBody,
      };
    },
  },

  // 开始筛选API
  {
    url: '/api/screening/condition/start',
    method: 'POST',
    body: req => {
      const { user_id, user_name, filter_condition } = req.body;
      mockFilterBody = {
        id: 1,
        user_id,
        user_name,
        filter_condition,
        status: 1,
      };
      return {
        error_code: 0,
        data: mockFilterBody,
      };
    },
  },

  // 停止筛选API
  {
    url: '/api/screening/condition/stop',
    method: 'PUT',
    body: req => {
      if (mockFilterBody) {
        mockFilterBody.status = 2;
      }
      return {
        error_code: 0,
        data: mockFilterBody,
      };
    },
  },

  // 股票池相关API
  // {
  //   url: '/api/pool/info',
  //   method: 'GET',
  //   body: () => {
  //     return {
  //       error_code: 0,
  //       data: mockPoolStocks,
  //     };
  //   },
  // },
  // {
  //   url: '/api/pool/info',
  //   method: 'POST',
  //   body: req => {
  //     const { instrument, instrument_name } = req.body;
  //     const newStock: PoolStock = {
  //       id: poolStockIdCounter++,
  //       instrument,
  //       instrument_name,
  //       trading_day: new Date().toISOString().split('T')[0],
  //       filter_msg: '手动添加',
  //       join_type: 2,
  //     };
  //     mockPoolStocks.push(newStock);
  //     return {
  //       error_code: 0,
  //       data: newStock,
  //     };
  //   },
  // },
  // {
  //   url: '/api/pool/info',
  //   method: 'DELETE',
  //   body: req => {
  //     const { id } = req.query;
  //     const index = mockPoolStocks.findIndex(stock => stock.id === Number(id));
  //     if (index > -1) {
  //       mockPoolStocks.splice(index, 1);
  //       return {
  //         error_code: 0,
  //       };
  //     }
  //     return {
  //       error_code: 1,
  //       error_msg: '股票不存在',
  //     };
  //   },
  // },
  // {
  //   url: '/api/pool/instrument',
  //   method: 'GET',
  //   body: req => {
  //     const { name } = req.query;
  //     if (!name) {
  //       return {
  //         error_code: 0,
  //         data: [],
  //       };
  //     }
  //     // 模拟搜索逻辑：根据股票代码或名称搜索
  //     const searchTerm = name.toLowerCase();
  //     const results = mockSearchStocks.filter(
  //       stock =>
  //         stock.instrument.toLowerCase().includes(searchTerm) ||
  //         stock.instrument_name.toLowerCase().includes(searchTerm),
  //     );
  //     return {
  //       error_code: 0,
  //       data: results,
  //     };
  //   },
  // },
]);
