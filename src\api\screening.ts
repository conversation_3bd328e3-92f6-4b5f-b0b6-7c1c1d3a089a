import type { FilterBody } from '@/types';
import http from './http';

class ScreeningService {
  private static base = '/screening/condition';

  static start(data: Partial<Pick<FilterBody, 'id'>> & Omit<FilterBody, 'id'>) {
    return http<FilterBody>(`${this.base}/start`, {
      method: 'POST',
      data,
    });
  }

  static stop(data: FilterBody) {
    return http<FilterBody>(`${this.base}/stop`, {
      method: 'PUT',
      data,
    });
  }

  static getFilterBody() {
    return http<FilterBody>(this.base);
  }
}

export default ScreeningService;
