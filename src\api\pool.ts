import type { PoolStock, QueryInstrumentResponse } from '@/types';
import http from './http';

class PoolService {
  private static base = '/pool';

  static queryInstrument(name: string) {
    return http<QueryInstrumentResponse[]>(`${this.base}/instrument`, {
      params: {
        name,
      },
    });
  }

  static getPoolStocks() {
    return http<PoolStock[]>(`${this.base}/info`);
  }

  static addPoolStock(data: Pick<PoolStock, 'instrument' | 'instrument_name'>) {
    return http<PoolStock>(`${this.base}/info`, {
      method: 'POST',
      data,
    });
  }

  static deletePoolStock(id: number) {
    return http<void>(`${this.base}/info`, {
      method: 'DELETE',
      params: {
        id,
      },
    });
  }
}

export default PoolService;
