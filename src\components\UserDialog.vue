<script setup lang="ts">
import { computed, ref, shallowRef, useTemplateRef, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormUser, UserInfo } from '@/types';
import { UserService } from '@/api';
import { Misc } from '@/script';

const { user } = defineProps<{
  user?: UserInfo;
}>();

const visible = defineModel<boolean>();

const emit = defineEmits<{
  success: [];
}>();

const disabled = shallowRef(false);

// 表单校验规则
const rules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  rePassword: [{ required: true, message: '请输入确认密码', trigger: 'blur' }],
};

const formRef = useTemplateRef('formRef');

const form = ref<FormUser>({
  username: '',
  password: '',
  rePassword: '',
  permission: 2,
});

// 是否显示手动入池权限表单项,仅当管理员创建/修改交易员时显示
const hasPermissionFormItem = computed(() => {
  if (!Misc.isAdmin()) return false;
  if (user) return user.user_type === 2;
  return true;
});

// 监听visible变化
watch(visible, val => {
  if (val) {
    if (user) {
      form.value = {
        username: user.user_name,
        password: '',
        rePassword: '',
        permission: user.permission,
      };
    }
  }
});

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async valid => {
    if (valid) {
      if (form.value.password !== form.value.rePassword) {
        ElMessage.error('两次输入的密码不一致');
      } else {
        disabled.value = true;
        if (user) {
          // 修改用户
          const { error_code } = await UserService.update({
            ...user,
            user_name: form.value.username,
            password: form.value.password,
          });
          if (error_code === 0) {
            ElMessage.success('修改成功');
            emit('success');
            handleClose();
          } else {
            ElMessage.error('修改失败');
          }
        } else {
          // 新建交易员，user_type固定为2
          const { error_code } = await UserService.create({
            user_name: form.value.username,
            password: form.value.password,
            permission: form.value.permission,
            user_type: 2,
          });
          if (error_code === 0) {
            ElMessage.success('新建成功');
            emit('success');
            handleClose();
          } else {
            ElMessage.error('新建失败');
          }
        }
        disabled.value = false;
      }
    }
  });
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
  form.value = {
    username: '',
    password: '',
    rePassword: '',
    permission: 2,
  };
  formRef.value?.resetFields();
};
</script>
<template>
  <el-dialog
    :model-value="visible"
    :title="user ? '修改密码' : '新建交易员'"
    width="300px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="90px">
      <el-form-item label="用户名" prop="username">
        <el-input v-model="form.username" placeholder="请输入用户名" />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="form.password" type="password" placeholder="请输入密码" />
      </el-form-item>
      <el-form-item label="确认密码" prop="rePassword">
        <el-input v-model="form.rePassword" type="password" placeholder="请再次输入密码" />
      </el-form-item>
      <el-form-item v-if="hasPermissionFormItem" label="手动入池" prop="permission">
        <el-radio-group v-model="form.permission">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="2">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="small" @click="handleClose">取消</el-button>
      <el-button size="small" type="primary" :disabled="disabled" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>
