import axios, { type AxiosRequestConfig } from 'axios';
import qs from 'qs';

const customAxios = axios.create({
  timeout: 1000 * 300,
  withCredentials: false,
  headers: {
    'X-Requested-With': 'XMLHttpRequest',
    Accept: 'application/json',
    'Content-Type': 'application/json',
  },
  transformRequest: [
    (data: object, headers) => {
      if (headers['Content-Type'] === 'application/json') {
        return JSON.stringify(data);
      } else if (headers['Content-Type'] === 'multipart/form-data') {
        return data;
      }
      return qs.stringify(data);
    },
  ],
});

// TODO: 预留请求/响应拦截器
customAxios.interceptors.request.use(
  config => {
    return config;
  },
  error => {
    return Promise.reject(error);
  },
);

customAxios.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    return Promise.reject(error);
  },
);

const http = async <T>(url: string, configs?: AxiosRequestConfig): Promise<HttpResponse<T>> => {
  try {
    const res = await customAxios(url, {
      ...configs,
      baseURL: (window as any).SERVER,
    });
    return res.data;
  } catch (error) {
    console.warn(error);
    return {
      error_code: -1,
      error_msg: '网络异常',
    };
  }
};

export interface HttpResponse<T> {
  error_code: number;
  error_msg?: string;
  data?: T;
}

export default http;
