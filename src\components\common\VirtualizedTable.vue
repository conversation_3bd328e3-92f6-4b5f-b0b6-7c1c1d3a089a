<script setup lang="tsx" generic="T extends AnyObject">
import type { AnyObject, ColumnDefinition, RowAction } from '@/types';
import type { FunctionalComponent } from 'vue';
import type { CheckboxValueType, RowEventHandlers } from 'element-plus';
import { ElCheckbox, TableV2FixedDir, TableV2SortOrder, type SortBy } from 'element-plus';
import { computed, ref, shallowRef } from 'vue';

const {
  columns,
  data,
  select = false,
  showIndex = false,
  identity = 'id',
  sort = { key: '', order: TableV2SortOrder.DESC },
  rowActions = [],
  rowActionWidth = 80,
  rowHeight = 36,
  fixed = false,
} = defineProps<{
  columns: ColumnDefinition<T>;
  data: T[];
  /** 是否显示序号列 */
  showIndex?: boolean;
  /** 是否显示选择列 */
  select?: boolean;
  /** 行数据的唯一标识字段 */
  identity?: keyof T;
  /** 默认排序列 */
  sort?: SortBy;
  /** 行操作 */
  rowActions?: RowAction<T>[];
  /** 行操作列宽度 */
  rowActionWidth?: number;
  /** 行高 */
  rowHeight?: number;
  /** 单元格宽度是自适应还是固定 */
  fixed?: boolean;
}>();

const emit = defineEmits<{
  'row-dblclick': [rowData: T];
  'row-click': [rowData: T];
}>();

const rowEventHandlers: RowEventHandlers = {
  onClick: ({ rowData }) => {
    clickedRow.value = rowData;
    emit('row-click', rowData);
  },
  onDblclick: ({ rowData }) => {
    emit('row-dblclick', rowData);
  },
};

// 默认排序状态
const sortState = shallowRef<SortBy>({
  key: sort.key,
  order: sort.order,
});

type SelectionCellProps = {
  value: boolean;
  intermediate?: boolean;
  onChange: (value: CheckboxValueType) => void;
};

// 已选择的行
const selectedRowsMap = ref<Record<string | number, boolean>>({});

// 单击选中的行
const clickedRow = shallowRef<T | null>(null);

// 已选择的列
const selectedRows = computed<T[]>(() => {
  if (!select) return [];
  return data.filter(row => {
    const key = row[identity] as string | number;
    return selectedRowsMap.value[key];
  });
});

// 如果有选择列，则行数据需要添加一个checked字段
const tableData = computed(() => {
  let computedData: T[] = [];

  if (!select) {
    computedData = data;
  } else {
    // 添加checked字段
    computedData = data.map(row => {
      const key = row[identity] as string | number;
      if (selectedRowsMap.value[key] === undefined) {
        selectedRowsMap.value[key] = false;
      }
      return { ...row, checked: selectedRowsMap.value[key] };
    });
  }

  // 排序
  if (sortState.value.key) {
    const key = sortState.value.key as keyof T;
    const order = sortState.value.order;

    computedData.sort((a, b) => {
      const aValue = a[key];
      const bValue = b[key];

      // 处理 null 和 undefined
      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;
      if (aValue === bValue) return 0;

      // 根据值的类型进行比较
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return order === TableV2SortOrder.ASC ? aValue - bValue : bValue - aValue;
      } else {
        // 字符串比较，支持中英文混合排序
        const result = String(aValue).localeCompare(String(bValue), 'zh-CN');
        return order === TableV2SortOrder.ASC ? result : -result;
      }
    });
  }

  return computedData;
});

// 添加选择列和操作列
const tableColumns = computed(() => {
  const fullColumns: ColumnDefinition<T> = [...columns];

  if (showIndex) {
    const indexColumn: ColumnDefinition<T>[0] = {
      key: 'index',
      title: '序号',
      width: 60,
      cellRenderer: ({ rowIndex }) => <span>{rowIndex + 1}</span>,
    };
    fullColumns.unshift(indexColumn);
  }

  if (select) {
    const selectionColumn: ColumnDefinition<T>[0] = {
      key: 'selection',
      title: '',
      width: 50,
      cellRenderer: ({ rowData }: { rowData: T }) => {
        return (
          <SelectionCell
            value={selectedRowsMap.value[rowData[identity]]}
            onChange={val => {
              const key = rowData[identity] as string | number;
              selectedRowsMap.value[key] = val as boolean;
            }}
          />
        );
      },
      headerCellRenderer: () => {
        const onChange = (value: CheckboxValueType) => {
          tableData.value.forEach(row => {
            const key = row[identity] as string | number;
            selectedRowsMap.value[key] = value as boolean;
          });
        };
        const allSelected = tableData.value.every(row => row.checked);
        const containsChecked = tableData.value.some(row => row.checked);
        return (
          <SelectionCell
            value={allSelected}
            intermediate={containsChecked && !allSelected}
            onChange={onChange}
          />
        );
      },
    };
    fullColumns.unshift(selectionColumn);
  }

  if (rowActions.length) {
    const actionsColumn: ColumnDefinition<T>[0] = {
      key: 'actions',
      title: '操作',
      fixed: TableV2FixedDir.RIGHT,
      width: rowActionWidth,
      cellRenderer: ({ rowData }: { rowData: T }) => {
        return (
          <div class="flex aic">
            {rowActions.map(action => {
              if (action.show && !action.show(rowData)) return null;
              return (
                <el-button
                  size="small"
                  onClick={(e: MouseEvent) => {
                    e.stopPropagation();
                    action.onClick(rowData);
                  }}
                  disabled={action.disabled && action.disabled(rowData)}
                  color={action.color}
                >
                  {action.label}
                </el-button>
              );
            })}
          </div>
        );
      },
    };
    fullColumns.push(actionsColumn);
  }

  return fullColumns;
});

const SelectionCell: FunctionalComponent<SelectionCellProps> = ({
  value,
  intermediate = false,
  onChange,
}) => {
  return <ElCheckbox onChange={onChange} modelValue={value} indeterminate={intermediate} />;
};

const getRowClass = ({ rowIndex }: { rowIndex: number }) => {
  if (clickedRow.value) {
    const key = clickedRow.value[identity] as string | number;
    if (tableData.value[rowIndex][identity] === key) {
      return 'important-bg-[var(--g-hover-row)]';
    }
  }
  return rowIndex % 2 === 0 ? 'bg-[var(--g-panel-bg)]' : 'bg-[var(--g-panel-bg2)]';
};

const onSort = (sortBy: SortBy) => {
  sortState.value = sortBy;
};

defineExpose({
  selectedRows,
});
</script>
<template>
  <div h-full flex="~ col">
    <div v-if="$slots.toolbar" class="toolbar" h-36 flex aic jcsb pl12 pr12 bg="[--g-panel-bg]">
      <slot name="toolbar"></slot>
    </div>
    <ElAutoResizer flex-1 min-h-1>
      <template #default="{ height, width }">
        <ElTableV2
          :columns="tableColumns"
          :data="tableData"
          :width="width"
          :height="height"
          :header-height="36"
          :row-height="rowHeight"
          :row-class="getRowClass"
          :row-event-handlers="rowEventHandlers"
          :sort-by="sortState"
          :fixed="fixed"
          v-bind="$attrs"
          @column-sort="onSort"
        />
      </template>
    </ElAutoResizer>
  </div>
</template>
